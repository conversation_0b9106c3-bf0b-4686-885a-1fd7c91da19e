{"name": "@kdt310722/utils", "type": "module", "version": "0.0.19", "packageManager": "pnpm@10.11.0", "description": "A collection of utility functions for JavaScript / TypeScript application", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/kdt310722/utils", "repository": "github:kdt310722/utils", "bugs": {"email": "<EMAIL>", "url": "https://github.com/kdt310722/utils/issues"}, "sideEffects": false, "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "default": "./index.js"}, "./*": {"types": "./*/index.d.ts", "require": "./*/index.cjs", "default": "./*/index.js"}}, "main": "index.js", "types": "index.d.ts", "files": ["*"], "engines": {"node": ">=22.15.0"}, "publishConfig": {"access": "public", "directory": "dist"}, "scripts": {"dev": "vitest watch", "build": "rimraf dist && tsup && tsc --project ./tsconfig.build.json", "test": "vitest run", "coverage": "vitest run --coverage", "release": "tsx scripts/release.ts && changelogen gh release && pnpm publish", "up": "ncu -i", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks", "prepublishOnly": "pnpm build"}, "dependencies": {"json5": "^2.2.3", "p-retry": "^6.2.1", "serialize-error": "^12.0.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@kdt310722/eslint-config": "^0.2.0", "@kdt310722/tsconfig": "^1.0.0", "@swc/core": "^1.11.29", "@types/node": "^22.15.24", "@vitest/coverage-v8": "^3.1.4", "changelogen": "^0.6.1", "eslint": "^9.27.0", "execa": "^9.6.0", "fast-glob": "^3.3.3", "lint-staged": "^16.1.0", "npm-check-updates": "^18.0.1", "only-allow": "^1.2.1", "rimraf": "^6.0.1", "simple-git-hooks": "^2.13.0", "tsup": "^8.5.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "commitlint": {"extends": "@commitlint/config-conventional"}, "simple-git-hooks": {"commit-msg": "npx --no -- commitlint --edit ${1}", "pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}