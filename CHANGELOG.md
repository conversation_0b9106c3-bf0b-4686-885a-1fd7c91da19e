# Changelog

All notable changes to this project will be documented in this file.
See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## v0.0.19

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.18...v0.0.19)

### 🚀 Enhancements

- Implement abortable promise functionality with comprehensive tests ([95a67f3](https://github.com/kdt310722/utils/commit/95a67f3))

### 💅 Refactors

- Rename tap to pTap and update export ([8a3b212](https://github.com/kdt310722/utils/commit/8a3b212))

### ❤️ Contributors

- DiepPk <<EMAIL>>

## v0.0.18

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.17...v0.0.18)

### 🚀 Enhancements

- Enhance deferred promise functionality and add comprehensive tests ([124c637](https://github.com/kdt310722/utils/commit/124c637))
- Add createDeferredWithTimeout function and corresponding tests ([359dc66](https://github.com/kdt310722/utils/commit/359dc66))
- Error utils ([e4e7351](https://github.com/kdt310722/utils/commit/e4e7351))

### 🏡 Chore

- Update deps ([bd0c8b0](https://github.com/kdt310722/utils/commit/bd0c8b0))
- Add instructions for some AI coding assistant tools ([519ace6](https://github.com/kdt310722/utils/commit/519ace6))
- Update .gitignore ([c329e8d](https://github.com/kdt310722/utils/commit/c329e8d))

### ❤️ Contributors

- DiepPk <<EMAIL>>

## v0.0.17

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.16...v0.0.17)

### 🚀 Enhancements

- **array:** Add some array utils ([931285f](https://github.com/kdt310722/utils/commit/931285f))
- **promise:** Add some promise utils ([778adb2](https://github.com/kdt310722/utils/commit/778adb2))

### 💅 Refactors

- Move formatDate to time module ([ba65dee](https://github.com/kdt310722/utils/commit/ba65dee))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.16

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.15...v0.0.16)

### 🚀 Enhancements

- **function:** Add "pipe" helper ([21667d1](https://github.com/kdt310722/utils/commit/21667d1))
- **promise:** Add "safeRace" util ([16a4d8c](https://github.com/kdt310722/utils/commit/16a4d8c))
- **promise:** Add "abortable" util ([51d3391](https://github.com/kdt310722/utils/commit/51d3391))
- **number:** Add "minMax" ([608e97d](https://github.com/kdt310722/utils/commit/608e97d))
- **node:** Add "fetch" util ([8c3d299](https://github.com/kdt310722/utils/commit/8c3d299))
- **number:** Add some formatters ([db33bee](https://github.com/kdt310722/utils/commit/db33bee))

### 🩹 Fixes

- **promise:** Retry options ([844b199](https://github.com/kdt310722/utils/commit/844b199))

### 🏡 Chore

- Update deps ([14be3ef](https://github.com/kdt310722/utils/commit/14be3ef))
- Update deps ([ae03770](https://github.com/kdt310722/utils/commit/ae03770))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.15

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.14...v0.0.15)

### 🚀 Enhancements

- **common:** Add ClassMethod utility type ([2707b7a](https://github.com/kdt310722/utils/commit/2707b7a))
- **promise:** Add create deferred options ([c3d3577](https://github.com/kdt310722/utils/commit/c3d3577))

### 🏡 Chore

- Update deps ([b42e2de](https://github.com/kdt310722/utils/commit/b42e2de))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.14

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.13...v0.0.14)

### 🚀 Enhancements

- Add isValidRange ([561209a](https://github.com/kdt310722/utils/commit/561209a))
- **number:** Support E number when format ([a189cec](https://github.com/kdt310722/utils/commit/a189cec))
- **number:** Check for number string ([697c0e2](https://github.com/kdt310722/utils/commit/697c0e2))

### 💅 Refactors

- Change Numberish type ([34f36b8](https://github.com/kdt310722/utils/commit/34f36b8))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.13

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.12...v0.0.13)

### 🚀 Enhancements

- Add some helpers to interact with async iterators ([a32bce2](https://github.com/kdt310722/utils/commit/a32bce2))
- Add once for event emitter ([c81b171](https://github.com/kdt310722/utils/commit/c81b171))
- Add option to throw instead of return fallback on tryCatch function ([8259bcc](https://github.com/kdt310722/utils/commit/8259bcc))
- Add transform helper ([5a15b83](https://github.com/kdt310722/utils/commit/5a15b83))
- Add bigint math ([31cab78](https://github.com/kdt310722/utils/commit/31cab78))
- Wait until next second ([b60e2a1](https://github.com/kdt310722/utils/commit/b60e2a1))
- Add tryCatchAsync ([f8f8fa4](https://github.com/kdt310722/utils/commit/f8f8fa4))
- Add humanizeNumber ([2e3ca72](https://github.com/kdt310722/utils/commit/2e3ca72))
- Add formatUsdCurrency ([8742cfb](https://github.com/kdt310722/utils/commit/8742cfb))

### 🏡 Chore

- Update deps ([abc9b5c](https://github.com/kdt310722/utils/commit/abc9b5c))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.12

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.11...v0.0.12)

## v0.0.11

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.10...v0.0.11)

### 🏡 Chore

- Update ([c4edbb2](https://github.com/kdt310722/utils/commit/c4edbb2))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.10

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.9...v0.0.10)

### 🚀 Enhancements

- **array:** Lru set ([7389e3b](https://github.com/kdt310722/utils/commit/7389e3b))
- **object:** Lru map ([33f4303](https://github.com/kdt310722/utils/commit/33f4303))

### 🩹 Fixes

- **promise:** Deferred promise ([1b54758](https://github.com/kdt310722/utils/commit/1b54758))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.9

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.8...v0.0.9)

### 🩹 Fixes

- Json ([139c8ea](https://github.com/kdt310722/utils/commit/139c8ea))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.8

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.7...v0.0.8)

### 🩹 Fixes

- Build ([5ba9a89](https://github.com/kdt310722/utils/commit/5ba9a89))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.7

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.6...v0.0.7)

### 🩹 Fixes

- **promise/timeout:** Clear timer when promise throw an error before timeout reached ([9eed2ce](https://github.com/kdt310722/utils/commit/9eed2ce))
- **promise/timeout:** Support add custom timeout error using function ([371dadb](https://github.com/kdt310722/utils/commit/371dadb))
- Style ([30d1371](https://github.com/kdt310722/utils/commit/30d1371))

### 🏡 Chore

- Update dependencies ([3e5659c](https://github.com/kdt310722/utils/commit/3e5659c))
- Update deps ([0632421](https://github.com/kdt310722/utils/commit/0632421))

### 🎨 Styles

- **promise/poll:** Poll options ([188c00d](https://github.com/kdt310722/utils/commit/188c00d))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.6

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.5...v0.0.6)

### 🚀 Enhancements

- **string:** Add shorten util ([f7529b8](https://github.com/kdt310722/utils/commit/f7529b8))
- **string:** Add truncate util ([88a3d3b](https://github.com/kdt310722/utils/commit/88a3d3b))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.5

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.4...v0.0.5)

### 🚀 Enhancements

- **common:** Add Mutable type ([2733ff7](https://github.com/kdt310722/utils/commit/2733ff7))
- **function:** Add tryCatch util ([2fcb862](https://github.com/kdt310722/utils/commit/2fcb862))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.4

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.3...v0.0.4)

### 🚀 Enhancements

- **object:** Add isPlainObject util ([bcd4ded](https://github.com/kdt310722/utils/commit/bcd4ded))
- **object:** Add some type utils to flatten an object ([4dc0606](https://github.com/kdt310722/utils/commit/4dc0606))
- **object:** Supported number key type in FlattenKeys type ([449d8db](https://github.com/kdt310722/utils/commit/449d8db))
- **object:** Add some type utils to help set the property value by path ([30bdbbe](https://github.com/kdt310722/utils/commit/30bdbbe))
- **object:** Add Paths type ([7d2d668](https://github.com/kdt310722/utils/commit/7d2d668))
- **object:** Add util to set object property value using a path ([80beca3](https://github.com/kdt310722/utils/commit/80beca3))
- **object:** Add util to get the object property value by path ([cbc8f58](https://github.com/kdt310722/utils/commit/cbc8f58))
- **object:** Add utilities to flatten/unflatten nested object ([a949d32](https://github.com/kdt310722/utils/commit/a949d32))

### 💅 Refactors

- **object:** Rename FollowPath to GetValue ([1cebeb0](https://github.com/kdt310722/utils/commit/1cebeb0))
- **object:** Path type must extend string instead of PropertyKey ([c416e2e](https://github.com/kdt310722/utils/commit/c416e2e))
- **object:** IsObject dont check for Date, Error and Regexp ([374036d](https://github.com/kdt310722/utils/commit/374036d))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.3

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.2...v0.0.3)

### 🩹 Fixes

- **object:** Check value is object ([d2aca96](https://github.com/kdt310722/utils/commit/d2aca96))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.2

[compare changes](https://github.com/kdt310722/utils/compare/v0.0.1...v0.0.2)

### 🏡 Chore

- **publish:** Using pnpm publish instead of npm ([ae864fd](https://github.com/kdt310722/utils/commit/ae864fd))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.1


### 🏡 Chore

- Initial commit ([c8a536f](https://github.com/kdt310722/utils/commit/c8a536f))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

